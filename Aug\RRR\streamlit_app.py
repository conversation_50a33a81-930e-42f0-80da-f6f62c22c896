import streamlit as st
import sqlite3
import re
import json
import hashlib
import os
import requests
import xml.etree.ElementTree as ET
from io import BytesIO
from datetime import datetime
from urllib.parse import quote
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError
import pypdf
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from pathlib import Path

# Lightweight RAG/mindmap dependencies (optional)
try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except Exception:
    SKLEARN_AVAILABLE = False

# Optional PDF rendering dependency (best-effort)
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except Exception:
    REPORTLAB_AVAILABLE = False

# Page configuration
st.set_page_config(
    page_title="PDF Research Assistant",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

from theme import inject_theme

# Apply the shared neon/glass theme
inject_theme(show_hero=True)

# Configuration
WHITELIST_PATTERNS = [
    r".*arxiv\.org",
    r".*\.pmc\.ncbi\.nlm\.nih\.gov", 
    r".*\.doaj\.org",
    r".*\.biorxiv\.org",
    r".*\.medrxiv\.org"
]

# Initialize session state
if 'current_results' not in st.session_state:
    st.session_state.current_results = []
if 'search_performed' not in st.session_state:
    st.session_state.search_performed = False
if 'processing_complete' not in st.session_state:
    st.session_state.processing_complete = False

def init_database():
    """Initialize SQLite database (returns a fresh connection).

    Note: this intentionally returns a new connection each call so callers
    may close it safely. Avoid caching the connection object because a
    cached sqlite3.Connection can become closed and reused across callers,
    causing `sqlite3.ProgrammingError: Cannot operate on a closed database`.
    """
    try:
        # Use a single file (sqlite3) name to avoid colliding with a directory
        # named `research_pdfs.db/` which would prevent creating the DB file.
        db_path = (Path(__file__).resolve().parent / "research_pdfs.sqlite3")
        connection = sqlite3.connect(str(db_path), check_same_thread=False)
        cursor = connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pdfs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                authors TEXT,
                abstract TEXT,
                url TEXT UNIQUE,
                pdf_content BLOB,
                extracted_text TEXT,
                search_query TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                repository TEXT,
                status TEXT DEFAULT 'downloaded'
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT,
                results_count INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Track unique knowledge items (fingerprints of extracted text)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS knowledge (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pdf_id INTEGER,
                fingerprint TEXT UNIQUE,
                length INTEGER,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Store proposed code changes by the AI (requires manual review by default)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS code_suggestions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                summary TEXT,
                patch TEXT,
                applied INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        connection.commit()
        return connection
        
    except Exception as e:
        st.error(f"Database initialization failed: {str(e)}")
        return None

def is_open_access_url(url):
    """Check if URL is from an open-access repository"""
    if not url:
        return False
    return any(re.match(pattern, url, re.IGNORECASE) for pattern in WHITELIST_PATTERNS)

def search_arxiv(query, max_results=10):
    """Search arXiv API and return results"""
    try:
        clean_query = quote(query.strip())
        api_url = f"https://export.arxiv.org/api/query?search_query={clean_query}&max_results={max_results}&sortBy=submittedDate&sortOrder=descending"
        
        request = Request(api_url)
        request.add_header('User-Agent', 'Mozilla/5.0 (compatible; PDFResearchAssistant/1.0)')
        
        response = urlopen(request, timeout=30)
        xml_content = response.read()
        root = ET.fromstring(xml_content)
        
        namespace = {'atom': 'http://www.w3.org/2005/Atom',
                    'arxiv': 'http://arxiv.org/schemas/atom'}
        
        results = []
        entries = root.findall('atom:entry', namespace)
        
        for entry in entries:
            try:
                title_elem = entry.find('atom:title', namespace)
                title = title_elem.text.strip().replace('\n', ' ') if title_elem is not None else "Unknown Title"
                
                authors = []
                author_elems = entry.findall('atom:author', namespace)
                for author in author_elems:
                    name_elem = author.find('atom:name', namespace)
                    if name_elem is not None:
                        authors.append(name_elem.text.strip())
                
                authors_str = ", ".join(authors) if authors else "Unknown Authors"
                
                summary_elem = entry.find('atom:summary', namespace)
                abstract = summary_elem.text.strip().replace('\n', ' ') if summary_elem is not None else ""
                
                pdf_url = None
                link_elems = entry.findall('atom:link', namespace)
                for link in link_elems:
                    if link.get('title') == 'pdf':
                        pdf_url = link.get('href')
                        break
                
                if pdf_url and is_open_access_url(pdf_url):
                    results.append({
                        "title": title,
                        "authors": authors_str,
                        "abstract": abstract,
                        "url": pdf_url,
                        "repository": "arXiv"
                    })
                    
            except Exception as e:
                continue
        
        return results
        
    except Exception as e:
        st.error(f"Error searching arXiv: {str(e)}")
        return []

def download_pdf_content(pdf_url):
    """Download PDF content with error handling"""
    try:
        request = Request(pdf_url)
        request.add_header('User-Agent', 'Mozilla/5.0 (compatible; PDFResearchAssistant/1.0)')
        request.add_header('Accept', 'application/pdf,*/*')
        
        response = urlopen(request, timeout=60)
        content = response.read()
        
        if not content.startswith(b'%PDF-'):
            raise ValueError("Downloaded file is not a valid PDF")
        
        return content
        
    except Exception as e:
        raise Exception(f"Error downloading PDF: {str(e)}")

def fingerprint_text(text: str) -> str:
    """Return a stable SHA256 fingerprint for a text blob."""
    if not text:
        return ""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()

def convert_url_to_pdf(url: str, out_path: str) -> str:
    """Download a URL, extract visible text, and write a simple PDF.

    Falls back with a clear message if ReportLab is not installed.
    Returns the path to the generated PDF.
    """
    if not REPORTLAB_AVAILABLE:
        raise RuntimeError("reportlab is not available; install reportlab to enable URL->PDF conversion")

    try:
        req = Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0')
        content = urlopen(req, timeout=30).read().decode('utf-8', errors='ignore')
        # Strip tags naively for a plaintext dump (simple fallback)
        text = re.sub('<[^<]+?>', '', content)

        c = canvas.Canvas(out_path, pagesize=letter)
        width, height = letter
        margin = 40
        y = height - margin
        for line in text.splitlines():
            # Wrap long lines
            if not line.strip():
                y -= 12
                continue
            # write and advance
            c.drawString(margin, y, line[:1000])
            y -= 12
            if y < margin:
                c.showPage()
                y = height - margin
        c.save()
        return out_path
    except Exception as e:
        raise RuntimeError(f"Failed to convert URL to PDF: {e}")

def openrouter_chat(prompt: str, model: str = "google/gemini-2.5-flash-image-preview:free", api_key: str | None = None):
    """Send a simple chat completion request to OpenRouter (if configured).

    The function prefers an explicit api_key argument; otherwise reads
    from the OPENROUTER_API_KEY environment variable.
    """
    key = api_key or os.environ.get('OPENROUTER_API_KEY')
    if not key:
        raise RuntimeError("OpenRouter API key not configured. Set OPENROUTER_API_KEY in env or pass api_key")

    url = "https://api.openrouter.ai/v1/chat/completions"
    headers = {
        'Authorization': f'Bearer {key}',
        'Content-Type': 'application/json'
    }
    payload = {
        'model': model,
        'messages': [{"role": "user", "content": prompt}],
        'max_tokens': 512
    }
    resp = requests.post(url, headers=headers, json=payload, timeout=30)
    resp.raise_for_status()
    data = resp.json()
    # Basic extraction for common OpenRouter response shape
    if 'choices' in data and len(data['choices']) > 0:
        return data['choices'][0].get('message', {}).get('content', '')
    return data


def ollama_chat(prompt: str, endpoint: str = None, model: str = 'ollama/gpt-4o-mini'):
    """Talk to a local Ollama instance if available.

    Endpoint should be like 'http://localhost:11434'
    """
    ep = endpoint or os.environ.get('OLLAMA_ENDPOINT')
    if not ep:
        raise RuntimeError('Ollama endpoint not configured')
    url = f"{ep}/api/generate"
    payload = {"model": model, "messages": [{"role": "user", "content": prompt}], "max_tokens": 512}
    resp = requests.post(url, json=payload, timeout=30)
    resp.raise_for_status()
    data = resp.json()
    # Ollama API shapes vary; attempt to extract text
    if isinstance(data, dict):
        return data.get('text') or json.dumps(data)
    return str(data)


def chat_with_provider(prompt: str, provider: str = None, model: str = None):
    prov = provider or st.session_state.get('ai_provider') or 'openrouter'
    if prov == 'openrouter':
        key = os.environ.get('OPENROUTER_API_KEY')
        mdl = model or os.environ.get('OPENROUTER_MODEL') or 'google/gemini-2.5-flash-image-preview:free'
        return openrouter_chat(prompt, model=mdl, api_key=key)
    elif prov == 'ollama':
        ep = os.environ.get('OLLAMA_ENDPOINT')
        mdl = model or 'ollama/gpt-4o-mini'
        return ollama_chat(prompt, endpoint=ep, model=mdl)
    elif prov == 'gemini':
        # Gemini via OpenRouter model param
        key = os.environ.get('OPENROUTER_API_KEY')
        mdl = model or os.environ.get('OPENROUTER_MODEL')
        return openrouter_chat(prompt, model=mdl, api_key=key)
    else:
        raise RuntimeError(f'Unsupported AI provider: {prov}')

def extract_text_from_pdf(pdf_content):
    """Extract text from PDF content"""
    try:
        pdf_stream = BytesIO(pdf_content)
        reader = pypdf.PdfReader(pdf_stream)
        
        extracted_text = ""
        total_pages = len(reader.pages)
        
        progress_bar = st.progress(0, text="Extracting text from PDF pages...")
        
        for page_num, page in enumerate(reader.pages):
            progress = (page_num + 1) / total_pages
            progress_bar.progress(progress, text=f"Processing page {page_num + 1} of {total_pages}")
            
            try:
                page_text = page.extract_text()
                if page_text and page_text.strip():
                    extracted_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            except Exception:
                continue
        
        progress_bar.empty()
        
        if not extracted_text.strip():
            return "No text could be extracted from this PDF"
        
        return extracted_text
        
    except Exception as e:
        return f"Error extracting text from PDF: {str(e)}"

def store_pdf_data(connection, title, authors, abstract, url, pdf_content, extracted_text, query, repository):
    """Store PDF data in database"""
    try:
        cursor = connection.cursor()
        
        cursor.execute("SELECT id FROM pdfs WHERE url = ?", (url,))
        existing = cursor.fetchone()
        
        if existing:
            st.info(f"📚 Paper already exists in library: {title[:50]}...")
            return existing[0]
        
        cursor.execute("""
            INSERT INTO pdfs (title, authors, abstract, url, pdf_content, 
                            extracted_text, search_query, repository)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (title, authors, abstract, url, pdf_content, extracted_text, query, repository))
        
        connection.commit()
        pdf_id = cursor.lastrowid

        # Log the search
        cursor.execute("INSERT INTO search_history (query, results_count) VALUES (?, ?)", (query, 1))
        connection.commit()

        # Record knowledge fingerprint and optionally trigger AI analysis
        try:
            fingerprint = fingerprint_text(extracted_text or "")
            if fingerprint:
                cursor.execute("INSERT OR IGNORE INTO knowledge (pdf_id, fingerprint, length) VALUES (?, ?, ?)",
                               (pdf_id, fingerprint, len(extracted_text or "")))
                connection.commit()

                # If AI analysis enabled in session, run it (non-blocking UI-friendly approach could be improved)
                if st.session_state.get('ai_analyze_enabled'):
                    try:
                        analysis = analyze_new_knowledge(fingerprint, extracted_text or "", title=title)
                        if analysis and isinstance(analysis, dict) and analysis.get('suggest_code'):
                            save_code_suggestion(analysis['summary'], analysis['suggest_code'])
                            if st.session_state.get('ai_auto_apply_code'):
                                apply_suggestion()
                    except Exception:
                        # Don't fail the store operation if AI errors
                        pass

        except Exception:
            pass

        return pdf_id
        
    except Exception as e:
        st.error(f"Error storing PDF data: {str(e)}")
        return None

def get_dashboard_statistics():
    """Get comprehensive statistics for the dashboard"""
    connection = init_database()
    if not connection:
        return {
            "total_pdfs": 0,
            "total_searches": 0,
            "total_characters": 0,
            "successful_extractions": 0,
            "repo_stats": {},
            "recent_pdfs": 0,
            "top_queries": [],
            "monthly_activity": [],
            "knowledge_score": 0,
            "success_rate": 0.0,
            "total_words": 0,
            "total_pages": 0,
        }
    
    cursor = connection.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM pdfs")
    total_pdfs = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM search_history")
    total_searches = cursor.fetchone()[0]
    
    cursor.execute("SELECT SUM(LENGTH(extracted_text)) FROM pdfs WHERE extracted_text IS NOT NULL")
    total_characters = cursor.fetchone()[0] or 0
    
    cursor.execute("SELECT COUNT(*) FROM pdfs WHERE LENGTH(extracted_text) > 1000")
    successful_extractions = cursor.fetchone()[0]
    
    cursor.execute("SELECT repository, COUNT(*) FROM pdfs GROUP BY repository")
    repo_stats = dict(cursor.fetchall())
    
    cursor.execute("SELECT COUNT(*) FROM pdfs WHERE date_added >= datetime('now', '-7 days')")
    recent_pdfs = cursor.fetchone()[0]
    
    cursor.execute("""
        SELECT query, COUNT(*) as count 
        FROM search_history 
        GROUP BY query 
        ORDER BY count DESC 
        LIMIT 5
    """)
    top_queries = cursor.fetchall()
    
    cursor.execute("""
        SELECT strftime('%Y-%m', date_added) as month, COUNT(*) 
        FROM pdfs 
        GROUP BY month 
        ORDER BY month DESC 
        LIMIT 12
    """)
    monthly_activity = cursor.fetchall()
    
    connection.close()
    
    knowledge_score = min(100, (total_pdfs * 10) + (total_characters / 10000))
    success_rate = (successful_extractions / max(total_pdfs, 1)) * 100
    
    return {
        "total_pdfs": total_pdfs,
        "total_searches": total_searches,
        "total_characters": total_characters,
        "successful_extractions": successful_extractions,
        "repo_stats": repo_stats,
        "recent_pdfs": recent_pdfs,
        "top_queries": top_queries,
        "monthly_activity": monthly_activity,
        "knowledge_score": int(knowledge_score),
        "success_rate": round(success_rate, 1),
        "total_words": int(total_characters / 5),
        "total_pages": int(total_characters / 2000)
    }

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('<h1 class="main-header">🔬 PDF Research Assistant</h1>', unsafe_allow_html=True)
    st.markdown("*Your intelligent research companion for academic papers*")
    
    # Sidebar for navigation and AI settings
    st.sidebar.title("📋 Navigation")
    page = st.sidebar.selectbox("Choose a page", 
                               ["🏠 Dashboard", "🔍 Search Papers", "📚 My Library", "📊 Analytics", "🧠 AI Console", "🗨️ AI Chat"])

    # AI / OpenRouter settings
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🤖 AI Settings")
    ai_provider = st.sidebar.selectbox("Provider", ["openrouter", "ollama", "gemini"], index=0)
    openrouter_key_input = st.sidebar.text_input("OpenRouter API Key", value=os.environ.get('OPENROUTER_API_KEY', ''), type='password')
    openrouter_default_model = st.sidebar.text_input("OpenRouter Model", value=os.environ.get('OPENROUTER_MODEL', 'google/gemini-2.5-flash-image-preview:free'))
    # optional Ollama endpoint (e.g., http://localhost:11434)
    ollama_endpoint = st.sidebar.text_input("Ollama Endpoint", value=os.environ.get('OLLAMA_ENDPOINT', ''))
    if openrouter_key_input:
        # store in env for immediate use during this session
        os.environ['OPENROUTER_API_KEY'] = openrouter_key_input
    if openrouter_default_model:
        os.environ['OPENROUTER_MODEL'] = openrouter_default_model
    if ollama_endpoint:
        os.environ['OLLAMA_ENDPOINT'] = ollama_endpoint

    ai_analyze_enabled = st.sidebar.checkbox("Analyze new documents with AI", value=False)
    ai_auto_apply_code = st.sidebar.checkbox("Auto-apply AI code suggestions", value=False, help="Applies suggested patches automatically (risky) - use with caution")
    # persist AI toggles in session state so background functions can read them
    st.session_state['ai_analyze_enabled'] = ai_analyze_enabled
    st.session_state['ai_auto_apply_code'] = ai_auto_apply_code
    st.session_state['ai_provider'] = ai_provider
    st.session_state['openrouter_model'] = os.environ.get('OPENROUTER_MODEL')
    # Dark mode toggle for chat UI
    if 'dark_mode' not in st.session_state:
        st.session_state['dark_mode'] = True
    dark_mode = st.sidebar.checkbox('Dark mode (chat)', value=st.session_state['dark_mode'])
    st.session_state['dark_mode'] = dark_mode
    
    # Quick stats in sidebar
    stats = get_dashboard_statistics()
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📊 Quick Stats")
    st.sidebar.metric("📚 Papers", stats['total_pdfs'])
    st.sidebar.metric("🧠 Knowledge Score", f"{stats['knowledge_score']}/100")
    st.sidebar.metric("✅ Success Rate", f"{stats['success_rate']}%")
    
    if page == "🏠 Dashboard":
        show_dashboard(stats)
    elif page == "🔍 Search Papers":
        show_search_page()
    elif page == "📚 My Library":
        show_library_page()
    elif page == "📊 Analytics":
        show_analytics_page(stats)
    elif page == "🧠 AI Console":
        show_ai_console()
    elif page == "🗨️ AI Chat":
        show_chat_page()


def show_chat_page():
    """Styled AI chat page modeled after the React example layout."""
    # Ensure session messages exist
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = [
            {"role": "ai", "content": "Hello, I am your AI assistant. How can I help you today?"}
        ]
    if 'chat_input' not in st.session_state:
        st.session_state.chat_input = ''

    dark = st.session_state.get('dark_mode', True)

    # Dynamic background CSS injection
    if dark:
        bg = 'linear-gradient(to bottom right, #000000, #0b1220, #ffffff10)'
    else:
        bg = 'linear-gradient(to bottom right, #f3f4f6, #ffffff, #e5e7eb)'

    st.markdown(f"""
    <style>
    .chat-app-shell {{
        min-height: 85vh;
        display:flex;
        align-items:center;
        justify-content:center;
        padding:24px;
        background: {bg};
    }}
    .chat-card {{
        width:95vw; max-width:1100px; height:85vh; border-radius:24px; padding:18px; box-shadow:0 20px 60px rgba(2,6,23,0.7);
        background: rgba(255,255,255,0.03); border:1px solid rgba(255,255,255,0.06); backdrop-filter: blur(8px);
    }}
    .chat-messages {{ overflow-y:auto; height:70%; padding-right:12px; }}
    .chat-bubble-user {{ display:inline-block; padding:10px 14px; border-radius:20px; background:linear-gradient(90deg,#6366f1,#8b5cf6); color:white; box-shadow:0 8px 24px rgba(99,102,241,0.18); max-width:70%; }}
    .chat-bubble-ai {{ display:inline-block; padding:10px 14px; border-radius:20px; background: rgba(255,255,255,0.06); color:#e6eef8; box-shadow:0 8px 24px rgba(2,6,23,0.6); max-width:70%; }}
    .chat-input-row {{ display:flex; gap:8px; align-items:center; }}
    .chat-input {{ flex:1; padding:12px 14px; border-radius:999px; border:1px solid rgba(255,255,255,0.06); background: rgba(0,0,0,0.4); color:inherit; }}
    .chat-actions {{ display:flex; gap:8px; }}
    </style>
    <div class="chat-app-shell">
      <div class="chat-card">
        <h2 style="margin:4px 0 8px 0; font-weight:800; font-size:1.4rem; background:linear-gradient(90deg,#00d4ff,#7c5cff); -webkit-background-clip:text; -webkit-text-fill-color:transparent;">🗨️ AI Chat</h2>
        <div class="glass-card" style="padding:8px; margin-bottom:8px;">Choose provider and settings using the controls in the sidebar.</div>
        <div class="chat-messages">
    """, unsafe_allow_html=True)

    # Render messages
    for i, msg in enumerate(st.session_state.chat_messages):
        if msg['role'] == 'user':
            st.markdown(f"<div style='display:flex; justify-content:flex-end; margin:6px 0'><div class='chat-bubble-user'>{msg['content']}</div></div>", unsafe_allow_html=True)
        else:
            st.markdown(f"<div style='display:flex; justify-content:flex-start; margin:6px 0'><div class='chat-bubble-ai'>{msg['content']}</div></div>", unsafe_allow_html=True)

    # Input row
    st.markdown("</div>")
    col1, col2 = st.columns([8,2])
    with col1:
        chat_input = st.text_input("", value=st.session_state.chat_input, placeholder="Type your message...", key='chat_input_field')
    with col2:
        send = st.button("Send", key='chat_send')
        clear = st.button("Clear", key='chat_clear')

    if clear:
        st.session_state.chat_messages = []
        st.experimental_rerun()

    if send and chat_input.strip():
        # append user
        st.session_state.chat_messages.append({"role": "user", "content": chat_input})
        st.session_state.chat_input = ''

        # Perform RAG retrieval if index exists and user toggled it
        rag_ctx = ''
        try:
            if 'use_rag' in st.session_state and st.session_state['use_rag']:
                out_dir = Path(__file__).resolve().parent / 'rag_index'
                if out_dir.exists():
                    import pickle
                    X = np.load(out_dir / 'matrix.npy')
                    with open(out_dir / 'meta.pkl', 'rb') as f:
                        data = pickle.load(f)
                    vectorizer = data.get('vectorizer')
                    if vectorizer:
                        qv = vectorizer.transform([chat_input]).toarray()
                        sims = (X @ qv.T).ravel()
                        idxs = sims.argsort()[::-1][:3]
                        pieces = []
                        for i in idxs:
                            pieces.append(data['meta'][i]['title'])
                        rag_ctx = "\n\nRetrieved: " + "; ".join(pieces)
        except Exception:
            rag_ctx = ''

        # call provider
        provider = st.sidebar.selectbox("Provider", options=['openrouter', 'ollama'], index=0)
        model = st.sidebar.text_input('Model', value=st.session_state.get('openrouter_model', os.environ.get('OPENROUTER_MODEL', 'google/gemini-2.5-flash-image-preview:free'))) if provider == 'openrouter' else st.sidebar.text_input('Ollama endpoint', value=st.session_state.get('ollama_endpoint', 'http://localhost:11434'))
        prompt = chat_input + ("\n\nContext:" + rag_ctx if rag_ctx else '')
        try:
            resp = chat_with_provider(prompt, provider=provider, model=model)
            ai_text = resp
        except Exception as e:
            ai_text = f"ERROR: {e}"

        st.session_state.chat_messages.append({"role": "ai", "content": ai_text})
        st.experimental_rerun()

    # Provide a simple send via provider using a separate button (non-blocking pattern)
    if 'chat_send_trigger' not in st.session_state:
        st.session_state.chat_send_trigger = False

    # Hidden area to close the card
    st.markdown("</div></div>", unsafe_allow_html=True)


def show_dashboard(stats):
    """Show main dashboard"""
    st.header("📊 Research Dashboard")
    
    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("📚 Total PDFs", stats['total_pdfs'], delta=stats['recent_pdfs'] if stats['recent_pdfs'] > 0 else None)
    
    with col2:
        st.metric("🧠 Knowledge Score", f"{stats['knowledge_score']}/100")
    
    with col3:
        st.metric("✅ Success Rate", f"{stats['success_rate']}%")
    
    with col4:
        st.metric("📄 Total Pages", stats['total_pages'])
    
    st.markdown("---")
    
    # Quick Actions
    st.subheader("🚀 Quick Actions")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔍 Search Papers", use_container_width=True):
            st.switch_page("pages/search.py")  # Updated for Streamlit 1.28+
    
    with col2:
        if st.button("📚 View Library", use_container_width=True):
            st.info("Navigate to 📚 My Library in the sidebar")
    
    with col3:
        if st.button("📊 Analytics", use_container_width=True):
            st.info("Navigate to 📊 Analytics in the sidebar")
    
    with col4:
        if st.button("💾 Export Data", use_container_width=True):
            export_data()
    
    # Repository breakdown
    if stats['repo_stats']:
        st.subheader("📊 Repository Breakdown")
        repo_df = pd.DataFrame(list(stats['repo_stats'].items()), columns=['Repository', 'Count'])
        fig = px.pie(repo_df, values='Count', names='Repository', 
                    title="Papers by Repository", 
                    color_discrete_sequence=['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4ecdc4'])
        st.plotly_chart(fig, use_container_width=True)
    
    # Recent activity chart
    if stats['monthly_activity']:
        st.subheader("📈 Monthly Activity")
        activity_df = pd.DataFrame(stats['monthly_activity'], columns=['Month', 'Papers'])
        activity_df = activity_df.sort_values('Month')
        
        fig = px.line(activity_df, x='Month', y='Papers', 
                     title="Papers Added Over Time",
                     markers=True,
                     color_discrete_sequence=['#667eea'])
        fig.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig, use_container_width=True)
    
    # Top queries
    if stats['top_queries']:
        st.subheader("🔥 Top Search Queries")
        for i, (query, count) in enumerate(stats['top_queries'], 1):
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"**{i}. {query}**")
                st.caption(f"{count} searches")
            with col2:
                if st.button(f"🔍 Search", key=f"search_again_{i}"):
                    st.session_state.search_query = query
                    st.info(f"Navigate to Search Papers and try: {query}")

def show_search_page():
    """Show search interface"""
    st.header("🔍 Search Academic Papers")
    
    # Search form
    st.subheader("📝 Enter Your Research Query")
    
    col1, col2 = st.columns([3, 1])
    with col1:
        query = st.text_input("Search terms:", 
                             placeholder="e.g., machine learning transformers, quantum computing",
                             value=st.session_state.get('search_query', ''))
    
    with col2:
        max_results = st.selectbox("Max Results", [10, 20, 50, 100, 200], index=1)
    
    auto_download = st.checkbox("🚀 Auto-download and process PDFs", value=True, 
                               help="Automatically download PDFs and extract text")
    
    if st.button("🔍 Search arXiv", use_container_width=True, type="primary"):
        if query.strip():
            st.session_state.current_results = []
            st.session_state.search_performed = True
            st.session_state.processing_complete = False
            
            with st.spinner("🔍 Searching arXiv database..."):
                results = search_arxiv(query, max_results)
            
            if not results:
                st.warning("❌ No papers found for your query. Try different keywords.")
                st.session_state.search_performed = False
            else:
                st.success(f"✅ Found {len(results)} papers!")
                st.session_state.current_results = results
                
                if auto_download:
                    process_search_results(query, results)
        else:
            st.error("Please enter a search query")
    
    # Display results
    if st.session_state.get('search_performed') and st.session_state.current_results:
        display_search_results(st.session_state.current_results, auto_download)

def process_search_results(query, results):
    """Process and download search results"""
    st.subheader("⚡ Processing Papers")
    
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    progress_bar = st.progress(0, text="Starting processing...")
    status_placeholder = st.empty()
    processed_count = 0
    
    for i, result in enumerate(results):
        progress = (i + 1) / len(results)
        progress_bar.progress(progress, text=f"Processing paper {i+1} of {len(results)}")
        
        status_placeholder.info(f"📄 Processing: {result['title'][:60]}...")
        
        try:
            # Download PDF
            pdf_content = download_pdf_content(result['url'])
            
            # Extract text
            extracted_text = extract_text_from_pdf(pdf_content)
            
            # Store in database
            pdf_id = store_pdf_data(
                connection, result['title'], result['authors'], 
                result['abstract'], result['url'], pdf_content, 
                extracted_text, query, result['repository']
            )
            
            if pdf_id:
                processed_count += 1
                status_placeholder.success(f"✅ Successfully processed: {result['title'][:60]}...")
            
        except Exception as e:
            status_placeholder.error(f"❌ Failed to process paper: {str(e)}")
            continue
    
    progress_bar.empty()
    status_placeholder.empty()
    
    if processed_count > 0:
        st.success(f"🎉 Successfully processed {processed_count} papers!")
        st.balloons()
        st.session_state.processing_complete = True
    
    connection.close()

def display_search_results(results, auto_download):
    """Display search results"""
    st.subheader(f"📄 Search Results ({len(results)} papers)")
    
    for i, result in enumerate(results):
        with st.expander(f"📄 {result['title']}", expanded=i == 0):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Authors: <AUTHORS>
                st.write(f"**Repository:** {result['repository']}")
                if result['abstract']:
                    st.write(f"**Abstract:** {result['abstract'][:300]}...")
                st.write(f"**URL:** {result['url']}")
            
            with col2:
                if not auto_download:
                    if st.button(f"📥 Download & Process", key=f"download_{i}"):
                        process_single_paper(result)

def process_single_paper(result):
    """Process a single paper"""
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    try:
        with st.spinner(f"Processing: {result['title'][:50]}..."):
            pdf_content = download_pdf_content(result['url'])
            extracted_text = extract_text_from_pdf(pdf_content)
            
            pdf_id = store_pdf_data(
                connection, result['title'], result['authors'], 
                result['abstract'], result['url'], pdf_content, 
                extracted_text, "manual", result['repository']
            )
            
            if pdf_id:
                st.success("✅ Paper processed successfully!")
                st.write("**Text Preview:**")
                st.text_area("Preview", extracted_text[:500] + "...", height=150, disabled=True)
    
    except Exception as e:
        st.error(f"❌ Processing failed: {str(e)}")
    
    finally:
        connection.close()

def show_library_page():
    """Show stored papers library"""
    st.header("📚 My Research Library")
    
    connection = init_database()
    if not connection:
        st.error("Unable to connect to database")
        return
    
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id, title, authors, repository, date_added, 
               LENGTH(extracted_text) as text_length, search_query, abstract
        FROM pdfs 
        ORDER BY date_added DESC
    """)
    
    papers = cursor.fetchall()
    connection.close()
    
    if not papers:
        st.info("📚 No papers in your library yet. Go to Search Papers to add some!")
        st.markdown("### 🚀 Get Started")
        if st.button("🔍 Search Your First Papers", use_container_width=True):
            st.info("Navigate to 🔍 Search Papers in the sidebar")
        return
    
    st.success(f"📊 **Total Papers in Library:** {len(papers)}")
    
    # Library controls
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        library_search = st.text_input("🔍 Search within your library:", 
                                      placeholder="Search titles, authors, or topics...")
    
    with col2:
        sort_by = st.selectbox("Sort by:", ["Newest First", "Oldest First", "Title A-Z"])
    
    with col3:
        if st.button("💾 Export Library", use_container_width=True):
            export_library_data(papers)
    
    # Filter papers
    filtered_papers = papers
    if library_search:
        filtered_papers = [
            paper for paper in papers 
            if library_search.lower() in paper[1].lower() or  # title
               library_search.lower() in paper[2].lower() or  # authors
               library_search.lower() in (paper[6] or "").lower() or  # search_query
               library_search.lower() in (paper[7] or "").lower()  # abstract
        ]
    
    # Sort papers
    if sort_by == "Oldest First":
        filtered_papers = list(reversed(filtered_papers))
    elif sort_by == "Title A-Z":
        filtered_papers = sorted(filtered_papers, key=lambda x: x[1].lower())
    
    st.markdown(f"**Showing {len(filtered_papers)} papers**")
    
    # Display papers
    for paper in filtered_papers:
        paper_id, title, authors, repository, date_added, text_length, search_query, abstract = paper
        
        with st.expander(f"📄 {title}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Authors: <AUTHORS>
                st.write(f"**Repository:** {repository}")
                st.write(f"**Added:** {date_added}")
                if search_query:
                    st.write(f"**Original Query:** {search_query}")
                if abstract:
                    st.write(f"**Abstract:** {abstract[:200]}...")
            
            with col2:
                st.metric("📄 Text Length", f"{text_length:,} chars")
                st.metric("📊 Est. Pages", f"{text_length // 2000}")
                
                if st.button(f"📖 Read Full Text", key=f"read_{paper_id}"):
                    show_full_paper_text(paper_id)
                
                if st.button(f"🗑️ Delete", key=f"delete_{paper_id}"):
                    delete_paper(paper_id)
                    st.rerun()

def show_full_paper_text(paper_id):
    """Show full extracted text for a paper"""
    connection = init_database()
    if not connection:
        return
    
    cursor = connection.cursor()
    cursor.execute("SELECT title, extracted_text, authors, url FROM pdfs WHERE id = ?", (paper_id,))
    result = cursor.fetchone()
    connection.close()
    
    if result:
        title, text, authors, url = result
        st.markdown("---")
        st.subheader(f"📖 {title}")
        st.caption(f"By: {authors}")
        st.caption(f"Source: {url}")
        
        # Text display with search
        search_text = st.text_input("🔍 Search within this paper:", key=f"search_in_{paper_id}")
        
        if search_text:
            # Highlight search terms (simple implementation)
            highlighted_text = text.replace(search_text, f"**{search_text}**")
            st.markdown(highlighted_text[:5000] + "..." if len(text) > 5000 else highlighted_text)
        else:
            st.text_area("Full Text", text, height=400, key=f"full_text_{paper_id}")
        
        # Download options
        col1, col2 = st.columns(2)
        with col1:
            st.download_button(
                "📄 Download as TXT",
                text,
                file_name=f"{title[:50]}.txt",
                mime="text/plain",
                key=f"download_text_{paper_id}"
            )
        
        with col2:
            if st.button("📋 Copy to Clipboard", key=f"copy_{paper_id}"):
                st.code(text[:1000] + "..." if len(text) > 1000 else text)

def delete_paper(paper_id):
    """Delete a paper from the library"""
    connection = init_database()
    if connection:
        cursor = connection.cursor()
        cursor.execute("DELETE FROM pdfs WHERE id = ?", (paper_id,))
        connection.commit()
        connection.close()
        st.success("🗑️ Paper deleted successfully!")

def export_library_data(papers):
    """Export library data"""
    import csv
    from io import StringIO
    
    output = StringIO()
    writer = csv.writer(output)
    writer.writerow(["ID", "Title", "Authors", "Repository", "Date Added", "Text Length", "Search Query"])
    
    for paper in papers:
        writer.writerow([
            paper[0], paper[1], paper[2], paper[3], 
            paper[4], paper[5], paper[6] or ""
        ])
    
    csv_data = output.getvalue()
    
    st.download_button(
        "📊 Download Library CSV",
        csv_data,
        file_name=f"research_library_{datetime.now().strftime('%Y%m%d')}.csv",
        mime="text/csv"
    )

def show_analytics_page(stats):
    """Show detailed analytics"""
    st.header("📊 Research Analytics Dashboard")
    
    # Key Performance Indicators
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("📚 Total Knowledge", f"{stats['total_characters']:,} chars")
        st.metric("📄 Estimated Words", f"{stats['total_words']:,}")
    
    with col2:
        st.metric("⚡ Processing Rate", f"{stats['success_rate']}% success")
        st.metric("🔍 Total Searches", stats['total_searches'])
    
    with col3:
        st.metric("📈 Recent Activity", f"{stats['recent_pdfs']} papers (7 days)")
        st.metric("🎯 Knowledge Score", f"{stats['knowledge_score']}/100")
    
    # Progress visualization
    st.subheader("📈 Progress Tracking")
    
    # Knowledge score progress
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = stats['knowledge_score'],
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Knowledge Score"},
        delta = {'reference': 50},
        gauge = {'axis': {'range': [None, 100]},
                'bar': {'color': "#667eea"},
                'steps': [
                    {'range': [0, 30], 'color': "lightgray"},
                    {'range': [30, 70], 'color': "yellow"},
                    {'range': [70, 100], 'color': "lightgreen"}],
                'threshold': {'line': {'color': "red", 'width': 4},
                              'thickness': 0.75, 'value': 90}}))
    st.plotly_chart(fig, use_container_width=True)
    
    # Success rate visualization
    if stats['total_pdfs'] > 0:
        success_fig = go.Figure()
        success_fig.add_trace(go.Bar(
            x=['Successful', 'Failed'],
            y=[stats['successful_extractions'], 
               stats['total_pdfs'] - stats['successful_extractions']],
            marker_color=['#28a745', '#dc3545']
        ))
        success_fig.update_layout(title="PDF Processing Success Rate")
        st.plotly_chart(success_fig, use_container_width=True)
    
    # Insights and recommendations
    st.subheader("💡 Smart Insights")
    
    if stats['total_characters'] > 50000:
        st.success("🧠 Excellent! You've processed over 50K characters of research content!")
    elif stats['total_characters'] > 10000:
        st.info("📚 Good progress! You've processed over 10K characters of research.")
    else:
        st.warning("🚀 Get started by searching and downloading more papers!")
    
    if stats['total_pdfs'] > 20:
        st.success("📚 Amazing research library! You have over 20 papers collected.")
    elif stats['total_pdfs'] > 5:
        st.info("📖 Good collection! You have a solid foundation of research papers.")
    
    if stats['success_rate'] > 90:
        st.success("⚡ Excellent processing success rate!")
    elif stats['success_rate'] > 70:
        st.info("✅ Good processing success rate.")
    elif stats['total_pdfs'] > 0:
        st.warning("⚠️ Some PDFs failed to process. Try different paper sources.")
    
    # Export analytics
    if st.button("📊 Export Analytics Report", use_container_width=True):
        export_analytics(stats)

def export_analytics(stats):
    """Export analytics report"""
    report = f"""
# PDF Research Assistant Analytics Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary Statistics
- Total PDFs: {stats['total_pdfs']}
- Total Searches: {stats['total_searches']}
- Knowledge Score: {stats['knowledge_score']}/100
- Success Rate: {stats['success_rate']}%
- Total Characters: {stats['total_characters']:,}
- Estimated Words: {stats['total_words']:,}
- Estimated Pages: {stats['total_pages']:,}

## Recent Activity
- Papers added in last 7 days: {stats['recent_pdfs']}

## Repository Breakdown
"""
    for repo, count in stats['repo_stats'].items():
        report += f"- {repo}: {count} papers\n"
    
    report += f"""
## Top Search Queries
"""
    for query, count in stats['top_queries']:
        report += f"- '{query}': {count} searches\n"
    
    st.download_button(
        "📊 Download Analytics Report",
        report,
        file_name=f"analytics_report_{datetime.now().strftime('%Y%m%d')}.md",
        mime="text/markdown"
    )

def export_data():
    """Export all data"""
    st.info("💾 Data export functionality activated!")
    
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM pdfs")
    papers = cursor.fetchall()
    
    cursor.execute("SELECT * FROM search_history")
    searches = cursor.fetchall()
    
    connection.close()
    
    # Create export data
    export_info = {
        "export_date": datetime.now().isoformat(),
        "total_papers": len(papers),
        "total_searches": len(searches),
        "papers": [
            {
                "id": paper[0],
                "title": paper[1],
                "authors": paper[2],
                "abstract": paper[3],
                "url": paper[4],
                "search_query": paper[7],
                "date_added": paper[8],
                "repository": paper[9],
                "status": paper[10]
            }
            for paper in papers
        ]
    }
    
    export_json = json.dumps(export_info, indent=2)
    
    st.download_button(
        "💾 Download Complete Database Export",
        export_json,
        file_name=f"research_assistant_export_{datetime.now().strftime('%Y%m%d')}.json",
        mime="application/json"
    )


def analyze_new_knowledge(fingerprint: str, text: str, title: str = None) -> dict:
    """Call AI to analyze newly added text and optionally return code suggestions.

    Returns a dict with keys like 'summary' and 'suggest_code' if available.
    This implementation uses OpenRouter by default and will raise if no key.
    """
    prompt = f"Analyze this document titled '{title or 'untitled'}' and summarize new knowledge. If you find an improvement or code change suggestion for the platform, return a JSON with keys: summary (short), suggest_code (a unified patch string), confidence (0-1). Text:\n\n{text[:3000]}"
    try:
        response = openrouter_chat(prompt)
        # Try to parse JSON out of response
        try:
            parsed = json.loads(response)
            return parsed
        except Exception:
            # fallback: return as text summary
            return {"summary": response}
    except Exception as e:
        return {"error": str(e)}


def save_code_suggestion(summary: str, patch_text: str):
    """Save a code suggestion to the DB for later review/appliance."""
    conn = init_database()
    if not conn:
        return False
    try:
        cur = conn.cursor()
        cur.execute("INSERT INTO code_suggestions (summary, patch) VALUES (?, ?)", (summary, patch_text))
        conn.commit()
        return True
    finally:
        conn.close()


def apply_suggestion(suggestion_id: int = None):
    """Apply a saved code suggestion. For safety this function only writes the patch
    to a file named `suggested_patch.diff` and marks the suggestion applied.
    Auto-apply (directly editing code) is intentionally disabled here to avoid
    unreviewed code changes. If `suggestion_id` is None, apply the latest.
    """
    conn = init_database()
    if not conn:
        return False
    try:
        cur = conn.cursor()
        if suggestion_id is None:
            cur.execute("SELECT id, patch FROM code_suggestions WHERE applied = 0 ORDER BY timestamp LIMIT 1")
            row = cur.fetchone()
        else:
            cur.execute("SELECT id, patch FROM code_suggestions WHERE id = ?", (suggestion_id,))
            row = cur.fetchone()
        if not row:
            return False
        sid, patch_text = row
        out_path = Path(__file__).resolve().parent / 'suggested_patch.diff'
        out_path.write_text(patch_text)
        cur.execute("UPDATE code_suggestions SET applied = 1 WHERE id = ?", (sid,))
        conn.commit()
        return True
    finally:
        conn.close()


def show_ai_console():
    """UI for viewing AI suggestions and interacting with them."""
    st.header("🧠 AI Console")

    conn = init_database()
    if not conn:
        st.error("Database unavailable")
        return

    cur = conn.cursor()
    cur.execute("SELECT id, summary, applied, timestamp FROM code_suggestions ORDER BY timestamp DESC LIMIT 50")
    suggestions = cur.fetchall()

    for sid, summary, applied, ts in suggestions:
        with st.expander(f"Suggestion #{sid} - {'APPLIED' if applied else 'PENDING'} - {ts}"):
            cur.execute("SELECT patch FROM code_suggestions WHERE id = ?", (sid,))
            patch = cur.fetchone()[0]
            st.code(patch[:10000])
            col1, col2 = st.columns(2)
            with col1:
                if st.button(f"Save patch to file", key=f"save_{sid}"):
                    out = Path(__file__).resolve().parent / f'suggestion_{sid}.diff'
                    out.write_text(patch)
                    st.success(f"Saved to {out}")
            with col2:
                if st.button(f"Mark applied", key=f"apply_{sid}"):
                    apply_suggestion(sid)
                    st.success("Marked applied and wrote suggested_patch.diff")

    conn.close()


    st.markdown("---")
    st.subheader("🔎 Retrieval-Augmented Generation (RAG) & Mindmap")
    if not SKLEARN_AVAILABLE:
        st.warning("RAG/mindmap features require numpy and scikit-learn. Install them to enable.")
        st.markdown("Run: `pip install numpy scikit-learn`")
        return

    # Load knowledge items
    conn = init_database()
    cur = conn.cursor()
    cur.execute("SELECT id, fingerprint, length FROM knowledge ORDER BY date_added DESC")
    knowledge_rows = cur.fetchall()
    cur.execute("SELECT pdf_id, fingerprint FROM knowledge")
    all_k = cur.fetchall()

    if not all_k:
        st.info("No knowledge items indexed yet. Process some papers first to populate the knowledge table.")
        conn.close()
        return

    # Build a simple in-memory corpus from pdfs table using fingerprints mapping
    corpus = []
    meta = []
    for k in all_k:
        pdf_id, fp = k
        cur.execute("SELECT title, extracted_text FROM pdfs WHERE id = ?", (pdf_id,))
        row = cur.fetchone()
        if row:
            title, text = row
            corpus.append(text or "")
            meta.append({"pdf_id": pdf_id, "title": title, "fingerprint": fp})

    conn.close()

    if not corpus:
        st.info("No extracted text available to index.")
        return

    # Buttons to build index
    if st.button("Build RAG index from current library"):
        with st.spinner("Building TF-IDF index..."):
            vectorizer = TfidfVectorizer(max_features=10000, stop_words='english')
            X = vectorizer.fit_transform(corpus)
            # persist to disk
            out_dir = Path(__file__).resolve().parent / 'rag_index'
            out_dir.mkdir(exist_ok=True)
            np.save(out_dir / 'matrix.npy', X.toarray())
            import pickle
            # Save the fitted vectorizer (contains idf and _tfidf) and meta
            with open(out_dir / 'meta.pkl', 'wb') as f:
                pickle.dump({'meta': meta, 'vectorizer': vectorizer}, f)
        st.success("RAG index built and saved to disk")

    st.markdown("**Query the RAG index**")
    q = st.text_input("Enter a question or search query for retrieval:")
    if st.button("Retrieve relevant passages") and q.strip():
        out_dir = Path(__file__).resolve().parent / 'rag_index'
        if not out_dir.exists():
            st.error("No RAG index found. Build it first.")
        else:
            import pickle
            X = np.load(out_dir / 'matrix.npy')
            with open(out_dir / 'meta.pkl', 'rb') as f:
                data = pickle.load(f)
            # Load the fitted vectorizer directly
            vectorizer = data.get('vectorizer')
            if vectorizer is None:
                st.error('Saved RAG index missing fitted vectorizer; please rebuild index.')
                return
            qv = vectorizer.transform([q]).toarray()
            # cosine similarity
            sims = (X @ qv.T).ravel()
            idxs = sims.argsort()[::-1][:10]
            st.markdown("**Top matches:**")
            for i in idxs:
                m = data['meta'][i]
                score = float(sims[i])
                st.write(f"- {m['title']} (score: {score:.4f})")

    # Mindmap generation
    st.markdown("**Generate Mindmap**")
    k_clusters = st.number_input("Number of clusters", min_value=2, max_value=20, value=6)
    if st.button("Generate Mindmap"):
        out_dir = Path(__file__).resolve().parent / 'rag_index'
        if not out_dir.exists():
            st.error("No RAG index found. Build it first.")
        else:
            X = np.load(out_dir / 'matrix.npy')
            # cluster
            kmeans = KMeans(n_clusters=min(k_clusters, X.shape[0]), random_state=42).fit(X)
            labels = kmeans.labels_
            clusters = {}
            for i, lab in enumerate(labels):
                clusters.setdefault(lab, []).append(data['meta'][i])

            # display mindmap as simple grouped list
            st.success("Mindmap (clusters) generated")
            for lab, items in clusters.items():
                with st.expander(f"Cluster {lab} — {len(items)} items"):
                    for itm in items:
                        st.write(f"• {itm['title']}")

if __name__ == "__main__":
    main()

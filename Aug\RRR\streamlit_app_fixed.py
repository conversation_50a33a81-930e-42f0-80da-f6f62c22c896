import streamlit as st
import sqlite3
import re
import json
import xml.etree.ElementTree as ET
from io import BytesIO
from datetime import datetime
from urllib.parse import quote
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError
import pypdf
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

# Page configuration
st.set_page_config(
    page_title="PDF Research Assistant",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling (legacy fallback)
try:
    # apply shared theme if available
    from theme import inject_theme
    inject_theme()
except Exception:
    pass

st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
    }
    .paper-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin: 0.5rem 0;
    }
    .stButton > button {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background: linear-gradient(45deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
""", unsafe_allow_html=True)

# Configuration
WHITELIST_PATTERNS = [
    r".*\.arxiv\.org",
    r".*\.pmc\.ncbi\.nlm\.nih\.gov", 
    r".*\.doaj\.org",
    r".*\.biorxiv\.org",
    r".*\.medrxiv\.org"
]

# Initialize session state
if 'current_results' not in st.session_state:
    st.session_state.current_results = []
if 'search_performed' not in st.session_state:
    st.session_state.search_performed = False
if 'processing_complete' not in st.session_state:
    st.session_state.processing_complete = False

@st.cache_resource
def init_database():
    """Initialize SQLite database"""
    try:
        connection = sqlite3.connect("research_pdfs.db", check_same_thread=False)
        cursor = connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pdfs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                authors TEXT,
                abstract TEXT,
                url TEXT UNIQUE,
                pdf_content BLOB,
                extracted_text TEXT,
                search_query TEXT,
                date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                repository TEXT,
                status TEXT DEFAULT 'downloaded'
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT,
                results_count INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        connection.commit()
        return connection
        
    except Exception as e:
        st.error(f"Database initialization failed: {str(e)}")
        return None

def is_open_access_url(url):
    """Check if URL is from an open-access repository"""
    if not url:
        return False
    return any(re.match(pattern, url, re.IGNORECASE) for pattern in WHITELIST_PATTERNS)

def search_arxiv(query, max_results=10):
    """Search arXiv API and return results"""
    try:
        clean_query = quote(query.strip())
        api_url = f"https://export.arxiv.org/api/query?search_query={clean_query}&max_results={max_results}&sortBy=submittedDate&sortOrder=descending"
        
        request = Request(api_url)
        request.add_header(\'User-Agent\', \'Mozilla/5.0 (compatible; PDFResearchAssistant/1.0)\')
        
        response = urlopen(request, timeout=30)
        xml_content = response.read()
        root = ET.fromstring(xml_content)
        
        namespace = {\'atom\': \'http://www.w3.org/2005/Atom\',
                    \'arxiv\': \'http://arxiv.org/schemas/atom\'}
        
        results = []
        entries = root.findall(\'atom:entry\', namespace)
        
        for entry in entries:
            try:
                title_elem = entry.find(\'atom:title\', namespace)
                title = title_elem.text.strip().replace(\'\n\', \'\') if title_elem is not None else "Unknown Title"
                
                authors = []
                author_elems = entry.findall(\'atom:author\', namespace)
                for author in author_elems:
                    name_elem = author.find(\'atom:name\', namespace)
                    if name_elem is not None:
                        authors.append(name_elem.text.strip())
                
                authors_str = ", ".join(authors) if authors else "Unknown Authors"
                
                summary_elem = entry.find(\'atom:summary\', namespace)
                abstract = summary_elem.text.strip().replace(\'\n\', \'\') if summary_elem is not None else ""
                
                pdf_url = None
                link_elems = entry.findall(\'atom:link\', namespace)
                for link in link_elems:
                    if link.get(\'title\') == \'pdf\':
                        pdf_url = link.get(\'href\')
                        break
                
                if pdf_url and is_open_access_url(pdf_url):
                    results.append({
                        "title": title,
                        "authors": authors_str,
                        "abstract": abstract,
                        "url": pdf_url,
                        "repository": "arXiv"
                    })
                    
            except Exception as e:
                continue
        
        return results
        
    except Exception as e:
        st.error(f"Error searching arXiv: {str(e)}")
        return []

def download_pdf_content(pdf_url):
    """Download PDF content with error handling"""
    try:
        request = Request(pdf_url)
        request.add_header(\'User-Agent\', \'Mozilla/5.0 (compatible; PDFResearchAssistant/1.0)\')
        request.add_header(\'Accept\', \'application/pdf,*/*\')
        
        response = urlopen(request, timeout=60)
        content = response.read()
        
        if not content.startswith(b\'%PDF-\'):
            raise ValueError("Downloaded file is not a valid PDF")
        
        return content
        
    except Exception as e:
        raise Exception(f"Error downloading PDF: {str(e)}")

def extract_text_from_pdf(pdf_content):
    """Extract text from PDF content"""
    try:
        pdf_stream = BytesIO(pdf_content)
        reader = pypdf.PdfReader(pdf_stream)
        
        extracted_text = ""
        total_pages = len(reader.pages)
        
        progress_bar = st.progress(0, text="Extracting text from PDF pages...")
        
        for page_num, page in enumerate(reader.pages):
            progress = (page_num + 1) / total_pages
            progress_bar.progress(progress, text=f"Processing page {page_num + 1} of {total_pages}")
            
            try:
                page_text = page.extract_text()
                if page_text and page_text.strip():
                    extracted_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            except Exception:
                continue
        
        progress_bar.empty()
        
        if not extracted_text.strip():
            return "No text could be extracted from this PDF"
        
        return extracted_text
        
    except Exception as e:
        return f"Error extracting text from PDF: {str(e)}"

def store_pdf_data(connection, title, authors, abstract, url, pdf_content, extracted_text, query, repository):
    """Store PDF data in database"""
    try:
        cursor = connection.cursor()
        
        cursor.execute("SELECT id FROM pdfs WHERE url = ?", (url,))
        existing = cursor.fetchone()
        
        if existing:
            st.info(f"📚 Paper already exists in library: {title[:50]}...")
            return existing[0]
        
        cursor.execute(\'\'\'
            INSERT INTO pdfs (title, authors, abstract, url, pdf_content, 
                            extracted_text, search_query, repository)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        \'\'\', (title, authors, abstract, url, pdf_content, extracted_text, query, repository))
        
        connection.commit()
        pdf_id = cursor.lastrowid
        
        # Log the search
        cursor.execute("INSERT INTO search_history (query, results_count) VALUES (?, ?)", (query, 1))
        connection.commit()
        
        return pdf_id
        
    except Exception as e:
        st.error(f"Error storing PDF data: {str(e)}")
        return None

def get_dashboard_statistics():
    """Get comprehensive statistics for the dashboard"""
    connection = init_database()
    if not connection:
        return {}
    
    cursor = connection.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM pdfs")
    total_pdfs = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM search_history")
    total_searches = cursor.fetchone()[0]
    
    cursor.execute("SELECT SUM(LENGTH(extracted_text)) FROM pdfs WHERE extracted_text IS NOT NULL")
    total_characters = cursor.fetchone()[0] or 0
    
    cursor.execute("SELECT COUNT(*) FROM pdfs WHERE LENGTH(extracted_text) > 1000")
    successful_extractions = cursor.fetchone()[0]
    
    cursor.execute("SELECT repository, COUNT(*) FROM pdfs GROUP BY repository")
    repo_stats = dict(cursor.fetchall())
    
    cursor.execute("SELECT COUNT(*) FROM pdfs WHERE date_added >= datetime(\'now\', \'-7 days\')")
    recent_pdfs = cursor.fetchone()[0]
    
    cursor.execute("""
        SELECT query, COUNT(*) as count 
        FROM search_history 
        GROUP BY query 
        ORDER BY count DESC 
        LIMIT 5
    """)
    top_queries = cursor.fetchall()
    
    cursor.execute("""
        SELECT strftime(\'%Y-%m\', date_added) as month, COUNT(*) 
        FROM pdfs 
        GROUP BY month 
        ORDER BY month DESC 
        LIMIT 12
    """)
    monthly_activity = cursor.fetchall()
    
    connection.close()
    
    knowledge_score = min(100, (total_pdfs * 10) + (total_characters / 10000))
    success_rate = (successful_extractions / max(total_pdfs, 1)) * 100
    
    return {
        "total_pdfs": total_pdfs,
        "total_searches": total_searches,
        "total_characters": total_characters,
        "successful_extractions": successful_extractions,
        "repo_stats": repo_stats,
        "recent_pdfs": recent_pdfs,
        "top_queries": top_queries,
        "monthly_activity": monthly_activity,
        "knowledge_score": int(knowledge_score),
        "success_rate": round(success_rate, 1),
        "total_words": int(total_characters / 5),
        "total_pages": int(total_characters / 2000)
    }

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown(\'<h1 class="main-header">🔬 PDF Research Assistant</h1>\', unsafe_allow_html=True)
    st.markdown("*Your intelligent research companion for academic papers*")
    
    # Sidebar for navigation
    st.sidebar.title("📋 Navigation")
    page = st.sidebar.selectbox("Choose a page", 
                               ["🏠 Dashboard", "🔍 Search Papers", "📚 My Library", "📊 Analytics"])
    
    # Quick stats in sidebar
    stats = get_dashboard_statistics()
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📊 Quick Stats")
    st.sidebar.metric("📚 Papers", stats[\'total_pdfs\'])
    st.sidebar.metric("🧠 Knowledge Score", f"{stats[\'knowledge_score\']}/100")
    st.sidebar.metric("✅ Success Rate", f"{stats[\'success_rate\']}%")
    
    if page == "🏠 Dashboard":
        show_dashboard(stats)
    elif page == "🔍 Search Papers":
        show_search_page()
    elif page == "📚 My Library":
        show_library_page()
    elif page == "📊 Analytics":
        show_analytics_page(stats)

def show_dashboard(stats):
    """Show main dashboard"""
    st.header("📊 Research Dashboard")
    
    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("📚 Total PDFs", stats[\'total_pdfs\'], delta=stats[\'recent_pdfs\'] if stats[\'recent_pdfs\'] > 0 else None)
    
    with col2:
        st.metric("🧠 Knowledge Score", f"{stats[\'knowledge_score\']}/100")
    
    with col3:
        st.metric("✅ Success Rate", f"{stats[\'success_rate\']}%")
    
    with col4:
        st.metric("📄 Total Pages", stats[\'total_pages\'])
    
    st.markdown("---")
    
    # Quick Actions
    st.subheader("🚀 Quick Actions")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔍 Search Papers", use_container_width=True):
            st.session_state.page = "🔍 Search Papers"
            st.rerun()
    
    with col2:
        if st.button("📚 View Library", use_container_width=True):
            st.session_state.page = "📚 My Library"
            st.rerun()
    
    with col3:
        if st.button("📊 Analytics", use_container_width=True):
            st.session_state.page = "📊 Analytics"
            st.rerun()
    
    with col4:
        if st.button("💾 Export Data", use_container_width=True):
            export_data()
    
    # Repository breakdown
    if stats[\'repo_stats\']:
        st.subheader("📊 Repository Breakdown")
        repo_df = pd.DataFrame(list(stats[\'repo_stats\'].items()), columns=[\'Repository\', \'Count\'])
        fig = px.pie(repo_df, values=\'Count\', names=\'Repository\', 
                    title="Papers by Repository", 
                    color_discrete_sequence=[\'#667eea\', \'#764ba2\', \'#f093fb\', \'#f5576c\', \'#4ecdc4\'])
        st.plotly_chart(fig, use_container_width=True)
    
    # Recent activity chart
    if stats[\'monthly_activity\']:
        st.subheader("📈 Monthly Activity")
        activity_df = pd.DataFrame(stats[\'monthly_activity\'], columns=[\'Month\', \'Papers\'])
        activity_df = activity_df.sort_values(\'Month\')
        
        fig = px.line(activity_df, x=\'Month\', y=\'Papers\', 
                     title="Papers Added Over Time",
                     markers=True,
                     color_discrete_sequence=[\'#667eea\'])
        fig.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig, use_container_width=True)
    
    # Top queries
    if stats[\'top_queries\']:
        st.subheader("🔥 Top Search Queries")
        for i, (query, count) in enumerate(stats[\'top_queries\'], 1):
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"**{i}. {query}**")
            with col2:
                if st.button(f"Search Again", key=f"search_again_{i}"):
                    st.session_state.search_query = query
                    st.session_state.page = "🔍 Search Papers"
                    st.rerun()
            st.caption(f"{count} searches")

def show_search_page():
    """Show search interface"""
    st.header("🔍 Search Academic Papers")
    
    # Search form
    st.subheader("📝 Enter Your Research Query")
    
    col1, col2 = st.columns([3, 1])
    with col1:
        query = st.text_input("Search terms:", 
                             placeholder="e.g., machine learning transformers, quantum computing",
                             value=st.session_state.get(\'search_query\', \'\'))
    
    with col2:
        max_results = st.selectbox("Max Results", [3, 5, 10, 15, 20], index=1)
    
    auto_download = st.checkbox("🚀 Auto-download and process PDFs", value=True, 
                               help="Automatically download PDFs and extract text")
    
    if st.button("🔍 Search arXiv", use_container_width=True, type="primary"):
        if query.strip():
            st.session_state.current_results = []
            st.session_state.search_performed = True
            st.session_state.processing_complete = False
            
            with st.spinner("🔍 Searching arXiv database..."):
                results = search_arxiv(query, max_results)
            
            if not results:
                st.warning("❌ No papers found for your query. Try different keywords.")
                st.session_state.search_performed = False
            else:
                st.success(f"✅ Found {len(results)} papers!")
                st.session_state.current_results = results
                
                if auto_download:
                    process_search_results(query, results)
        else:
            st.error("Please enter a search query")
    
    # Display results
    if st.session_state.get(\'search_performed\') and st.session_state.current_results:
        display_search_results(st.session_state.current_results, auto_download)

def process_search_results(query, results):
    """Process and download search results"""
    st.subheader("⚡ Processing Papers")
    
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    progress_bar = st.progress(0, text="Starting processing...")
    status_placeholder = st.empty()
    processed_count = 0
    
    for i, result in enumerate(results):
        progress = (i + 1) / len(results)
        progress_bar.progress(progress, text=f"Processing paper {i+1} of {len(results)}")
        
        status_placeholder.info(f"📄 Processing: {result[\'title\'][:60]}...")
        
        try:
            # Download PDF
            pdf_content = download_pdf_content(result[\'url\'])
            
            # Extract text
            extracted_text = extract_text_from_pdf(pdf_content)
            
            # Store in database
            pdf_id = store_pdf_data(
                connection, result[\'title\'], result[\'authors\'], 
                result[\'abstract\'], result[\'url\'], pdf_content, 
                extracted_text, query, result[\'repository\']
            )
            
            if pdf_id:
                processed_count += 1
                status_placeholder.success(f"✅ Successfully processed: {result[\'title\'][:60]}...")
            
        except Exception as e:
            status_placeholder.error(f"❌ Failed to process paper: {str(e)}")
            continue
    
    progress_bar.empty()
    status_placeholder.empty()
    
    if processed_count > 0:
        st.success(f"🎉 Successfully processed {processed_count} papers!")
        st.balloons()
        st.session_state.processing_complete = True
    
    connection.close()

def display_search_results(results, auto_download):
    """Display search results"""
    st.subheader(f"📄 Search Results ({len(results)} papers)")
    
    for i, result in enumerate(results):
        with st.expander(f"📄 {result[\'title\']}", expanded=i == 0):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Authors: <AUTHORS>
                st.write(f"**Repository:** {result[\'repository\']}")
                if result[\'abstract\']:
                    st.write(f"**Abstract:** {result[\'abstract\'][:300]}...")
                st.write(f"**URL:** {result[\'url\']}")
            
            with col2:
                if not auto_download:
                    if st.button(f"📥 Download & Process", key=f"download_{i}"):
                        process_single_paper(result)

def process_single_paper(result):
    """Process a single paper"""
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    try:
        with st.spinner(f"Processing: {result[\'title\'][:50]}..."):
            pdf_content = download_pdf_content(result[\'url\'])
            extracted_text = extract_text_from_pdf(pdf_content)
            
            pdf_id = store_pdf_data(
                connection, result[\'title\'], result[\'authors\'], 
                result[\'abstract\'], result[\'url\'], pdf_content, 
                extracted_text, "manual", result[\'repository\']
            )
            
            if pdf_id:
                st.success("✅ Paper processed successfully!")
                st.write("**Text Preview:**")
                st.text_area("Preview", extracted_text[:500] + "...", height=150, disabled=True)
    
    except Exception as e:
        st.error(f"❌ Processing failed: {str(e)}")
    
    finally:
        connection.close()

def show_library_page():
    """Show stored papers library"""
    st.header("📚 My Research Library")
    
    connection = init_database()
    if not connection:
        st.error("Unable to connect to database")
        return
    
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id, title, authors, repository, date_added, 
               LENGTH(extracted_text) as text_length, search_query, abstract
        FROM pdfs 
        ORDER BY date_added DESC
    """)
    
    papers = cursor.fetchall()
    connection.close()
    
    if not papers:
        st.info("📚 No papers in your library yet. Go to Search Papers to add some!")
        st.markdown("### 🚀 Get Started")
        if st.button("🔍 Search Your First Papers", use_container_width=True):
            st.session_state.page = "🔍 Search Papers"
            st.rerun()
        return
    
    st.success(f"📊 **Total Papers in Library:** {len(papers)}")
    
    # Library controls
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        library_search = st.text_input("🔍 Search within your library:", 
                                      placeholder="Search titles, authors, or topics...")
    
    with col2:
        sort_by = st.selectbox("Sort by:", ["Newest First", "Oldest First", "Title A-Z"])
    
    with col3:
        if st.button("💾 Export Library", use_container_width=True):
            export_library_data(papers)
    
    # Filter papers
    filtered_papers = papers
    if library_search:
        filtered_papers = [
            paper for paper in papers 
            if library_search.lower() in paper[1].lower() or  # title
               library_search.lower() in paper[2].lower() or  # authors
               library_search.lower() in (paper[6] or "").lower() or  # search_query
               library_search.lower() in (paper[7] or "").lower()  # abstract
        ]
    
    # Sort papers
    if sort_by == "Oldest First":
        filtered_papers = list(reversed(filtered_papers))
    elif sort_by == "Title A-Z":
        filtered_papers = sorted(filtered_papers, key=lambda x: x[1].lower())
    
    st.markdown(f"**Showing {len(filtered_papers)} papers**")
    
    # Display papers
    for paper in filtered_papers:
        paper_id, title, authors, repository, date_added, text_length, search_query, abstract = paper
        
        with st.expander(f"📄 {title}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Authors: <AUTHORS>
                st.write(f"**Repository:** {repository}")
                st.write(f"**Added:** {date_added}")
                if search_query:
                    st.write(f"**Original Query:** {search_query}")
                if abstract:
                    st.write(f"**Abstract:** {abstract[:200]}...")
            
            with col2:
                st.metric("📄 Text Length", f"{text_length:,} chars")
                st.metric("📊 Est. Pages", f"{text_length // 2000}")
                
                if st.button(f"📖 Read Full Text", key=f"read_{paper_id}"):
                    show_full_paper_text(paper_id)
                
                if st.button(f"🗑️ Delete", key=f"delete_{paper_id}"):
                    delete_paper(paper_id)
                    st.rerun()

def show_full_paper_text(paper_id):
    """Show full extracted text for a paper"""
    connection = init_database()
    if not connection:
        return
    
    cursor = connection.cursor()
    cursor.execute("SELECT title, extracted_text, authors, url FROM pdfs WHERE id = ?", (paper_id,))
    result = cursor.fetchone()
    connection.close()
    
    if result:
        title, text, authors, url = result
        st.markdown("---")
        st.subheader(f"📖 {title}")
        st.caption(f"By: {authors}")
        st.caption(f"Source: {url}")
        
        # Text display with search
        search_text = st.text_input("🔍 Search within this paper:", key=f"search_in_{paper_id}")
        
        if search_text:
            # Highlight search terms (simple implementation)
            highlighted_text = text.replace(search_text, f"**{search_text}**")
            st.markdown(highlighted_text[:5000] + "..." if len(text) > 5000 else highlighted_text)
        else:
            st.text_area("Full Text", text, height=400, key=f"full_text_{paper_id}")
        
        # Download options
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 Download Text", key=f"download_text_{paper_id}"):
                st.download_button(
                    "📄 Download as TXT",
                    text,
                    file_name=f"{title[:50]}.txt",
                    mime="text/plain"
                )
        
        with col2:
            if st.button("📋 Copy to Clipboard", key=f"copy_{paper_id}"):
                st.code(text[:1000] + "..." if len(text) > 1000 else text)

def delete_paper(paper_id):
    """Delete a paper from the library"""
    connection = init_database()
    if connection:
        cursor = connection.cursor()
        cursor.execute("DELETE FROM pdfs WHERE id = ?", (paper_id,))
        connection.commit()
        connection.close()
        st.success("🗑️ Paper deleted successfully!")

def export_library_data(papers):
    """Export library data"""
    import csv
    from io import StringIO
    
    output = StringIO()
    writer = csv.writer(output)
    writer.writerow(["ID", "Title", "Authors", "Repository", "Date Added", "Text Length", "Search Query"])
    
    for paper in papers:
        writer.writerow([
            paper[0], paper[1], paper[2], paper[3], 
            paper[4], paper[5], paper[6] or ""
        ])
    
    csv_data = output.getvalue()
    
    st.download_button(
        "📊 Download Library CSV",
        csv_data,
        file_name=f"research_library_{datetime.now().strftime('%Y%m%d')}.csv",
        mime="text/csv"
    )

def show_analytics_page(stats):
    """Show detailed analytics"""
    st.header("📊 Research Analytics Dashboard")
    
    # Key Performance Indicators
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("📚 Total Knowledge", f"{stats[\'total_characters\']:,} chars")
        st.metric("📄 Estimated Words", f"{stats[\'total_words\']:,}")
    
    with col2:
        st.metric("⚡ Processing Rate", f"{stats[\'success_rate\']}% success")
        st.metric("🔍 Total Searches", stats[\'total_searches\'])
    
    with col3:
        st.metric("📈 Recent Activity", f"{stats[\'recent_pdfs\']} papers (7 days)")
        st.metric("🎯 Knowledge Score", f"{stats[\'knowledge_score\']}/100")
    
    # Progress visualization
    st.subheader("📈 Progress Tracking")
    
    # Knowledge score progress
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = stats[\'knowledge_score\'],
        domain = {\'x\': [0, 1], \'y\': [0, 1]},
        title = {\'text\': "Knowledge Score"},
        delta = {\'reference\': 50},
        gauge = {\'axis\': {\'range\': [None, 100]},
                \'bar\': {\'color\': "#667eea"},
                \'steps\': [
                    {\'range\': [0, 30], \'color\': "lightgray"},
                    {\'range\': [30, 70], \'color\': "yellow"},
                    {\'range\': [70, 100], \'color\': "lightgreen"}],
                \'threshold\': {\'line\': {\'color\': "red", \'width\': 4},
                              \'thickness\': 0.75, \'value\': 90}}))
    st.plotly_chart(fig, use_container_width=True)
    
    # Success rate visualization
    if stats[\'total_pdfs\'] > 0:
        success_fig = go.Figure()
        success_fig.add_trace(go.Bar(
            x=[\'Successful\', \'Failed\'],
            y=[stats[\'successful_extractions\'], 
               stats[\'total_pdfs\'] - stats[\'successful_extractions\']],
            marker_color=[\'#28a745\', \'#dc3545\']
        ))
        success_fig.update_layout(title="PDF Processing Success Rate")
        st.plotly_chart(success_fig, use_container_width=True)
    
    # Insights and recommendations
    st.subheader("💡 Smart Insights")
    
    if stats[\'total_characters\'] > 50000:
        st.success("🧠 Excellent! You\'ve processed over 50K characters of research content!")
    elif stats[\'total_characters\'] > 10000:
        st.info("📚 Good progress! You\'ve processed over 10K characters of research.")
    else:
        st.warning("🚀 Get started by searching and downloading more papers!")
    
    if stats[\'total_pdfs\'] > 20:
        st.success("📚 Amazing research library! You have over 20 papers collected.")
    elif stats[\'total_pdfs\'] > 5:
        st.info("📖 Good collection! You have a solid foundation of research papers.")
    
    if stats[\'success_rate\'] > 90:
        st.success("⚡ Excellent processing success rate!")
    elif stats[\'success_rate\'] > 70:
        st.info("✅ Good processing success rate.")
    elif stats[\'total_pdfs\'] > 0:
        st.warning("⚠️ Some PDFs failed to process. Try different paper sources.")
    
    # Export analytics
    if st.button("📊 Export Analytics Report", use_container_width=True):
        export_analytics(stats)

def export_analytics(stats):
    """Export analytics report"""
    report = f"""
# PDF Research Assistant Analytics Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary Statistics
- Total PDFs: {stats['total_pdfs']}
- Total Searches: {stats['total_searches']}
- Knowledge Score: {stats['knowledge_score']}/100
- Success Rate: {stats['success_rate']}%
- Total Characters: {stats['total_characters']:,}
- Estimated Words: {stats['total_words']:,}
- Estimated Pages: {stats['total_pages']:,}

## Recent Activity
- Papers added in last 7 days: {stats['recent_pdfs']}

## Repository Breakdown
"""
    for repo, count in stats['repo_stats'].items():
        report += f"- {repo}: {count} papers\n"
    
    report += f"""
## Top Search Queries
"""
    for query, count in stats['top_queries']:
        report += f"- '{query}': {count} searches\n"
    
    st.download_button(
        "📊 Download Analytics Report",
        report,
        file_name=f"analytics_report_{datetime.now().strftime('%Y%m%d')}.md",
        mime="text/markdown"
    )

def export_data():
    """Export all data"""
    st.info("💾 Data export functionality activated!")
    
    connection = init_database()
    if not connection:
        st.error("Database connection failed")
        return
    
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM pdfs")
    papers = cursor.fetchall()
    
    cursor.execute("SELECT * FROM search_history")
    searches = cursor.fetchall()
    
    connection.close()
    
    # Create export data
    export_info = {
        "export_date": datetime.now().isoformat(),
        "total_papers": len(papers),
        "total_searches": len(searches),
        "papers": [
            {
                "id": paper[0],
                "title": paper[1],
                "authors": paper[2],
                "abstract": paper[3],
                "url": paper[4],
                "search_query": paper[7],
                "date_added": paper[8],
                "repository": paper[9],
                "status": paper[10]
            }
            for paper in papers
        ]
    }
    
    export_json = json.dumps(export_info, indent=2)
    
    st.download_button(
        "💾 Download Complete Database Export",
        export_json,
        file_name=f"research_assistant_export_{datetime.now().strftime('%Y%m%d')}.json",
        mime="application/json"
    )

if __name__ == "__main__":
    main()

import streamlit as st

def inject_theme(show_hero: bool = False):
    """Inject the project's neon/glass CSS theme into the current Streamlit page.

    Call this at the top of any page or app entrypoint to get a consistent look.
    """
    st.markdown(r"""
    <style>
        :root{
            --accent1: #7c5cff; /* purple */
            --accent2: #00d4ff; /* cyan */
            --accent3: #ff66c4; /* pink */
            --glass-bg: rgba(255,255,255,0.06);
            --glass-border: rgba(255,255,255,0.08);
        }
        html, body, [class*="css"] > .main {
            background: radial-gradient( circle at 10% 10%, rgba(124,92,255,0.12), transparent 10% ),
                        radial-gradient( circle at 90% 90%, rgba(0,212,255,0.08), transparent 10% ),
                        linear-gradient(135deg, #0f1724 0%, #071028 60%);
            color: #e6eef8;
            min-height:100vh;
            background-attachment: fixed;
            font-family: Inter, ui-sans-serif, system-ui, -apple-system, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>;
        }
        .app-background-overlay {
            position: fixed; inset: 0; pointer-events: none; z-index: 0;
            background-image: radial-gradient(rgba(255,255,255,0.02) 1px, transparent 1px);
            background-size: 80px 80px; opacity: 0.6;
            mix-blend-mode: overlay;
        }
        .glass-card, .paper-card, .metric-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(6px) saturate(120%);
            -webkit-backdrop-filter: blur(6px) saturate(120%);
            border-radius: 14px;
            box-shadow: 0 10px 30px rgba(2,6,23,0.6), inset 0 1px 0 rgba(255,255,255,0.02);
            padding: 1rem;
            color: #e6eef8;
        }
        button, input[type="button"], input[type="submit"], .stButton > button, .stDownloadButton > button {
            background: linear-gradient(90deg, rgba(124,92,255,0.12), rgba(0,212,255,0.08));
            color: white !important;
            border: 1px solid rgba(255,255,255,0.06) !important;
            border-radius: 12px !important;
            padding: 10px 16px !important;
            font-weight: 700 !important;
            letter-spacing: 0.2px;
            transition: transform 0.16s ease, box-shadow 0.16s ease, filter 0.16s ease;
            box-shadow: 0 8px 24px rgba(2,6,23,0.6), 0 0 18px rgba(124,92,255,0.06) inset;
        }
        button:hover, input[type="button"]:hover, input[type="submit"]:hover, .stButton > button:hover, .stDownloadButton > button:hover{
            transform: translateY(-4px) scale(1.02) !important;
            box-shadow: 0 18px 45px rgba(2,6,23,0.7), 0 0 36px rgba(124,92,255,0.16), 0 0 8px rgba(0,212,255,0.06) inset !important;
            filter: saturate(120%);
        }
        .stExpander > .stExpanderHeader, .stExpander {
            border-radius: 12px; margin-bottom: 0.6rem;
            border: 1px solid rgba(255,255,255,0.04);
            background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
            box-shadow: 0 8px 20px rgba(2,6,23,0.6);
        }
        input, textarea, .stTextInput>div>input {
            border-radius: 10px !important;
            background: rgba(255,255,255,0.03) !important;
            color: #e6eef8 !important;
            border: 1px solid rgba(255,255,255,0.04) !important;
        }
        .muted { color: rgba(230,238,248,0.6); }
        .block-container, .sidebar .block-container { position: relative; z-index: 1; }
    </style>
    <div class="app-background-overlay"></div>
    """, unsafe_allow_html=True)

    if show_hero:
        hero_html = (
            '<div class="glass-card" style="margin:1rem 0; display:block;">'
            '<h2 style="margin:0 0 6px 0; font-weight:800;">✨ Neon Research Assistant</h2>'
            '<p class="muted" style="margin:0">Consistent neon glass theme applied across pages.</p>'
            '</div>'
        )
        st.markdown(hero_html, unsafe_allow_html=True)
